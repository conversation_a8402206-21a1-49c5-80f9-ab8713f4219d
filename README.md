# 气缸控制系统

一个基于Python的气缸控制系统，通过USB接口控制外部电路，监听传感器输入，自动控制气缸执行工作周期。

## 功能特性

### 硬件控制功能
- 通过USB接口与主机连接，控制外部电路的通断
- 监听传感器输入信号，当检测到信号时自动接通电路
- 电路驱动电磁阀，控制气缸执行一个完整的工作周期

### 用户界面功能
- 显示气缸运动的累计次数计数器
- 提供重置计数功能的按钮
- 实时显示电路通断状态（开/关状态指示）
- 实时显示气缸工作状态
- 实时显示传感器触发状态
- 系统日志显示
- 手动启动工作周期功能
- 紧急停止功能

## 系统要求

- Python 3.8 或更高版本
- Windows/Linux/macOS 操作系统
- USB串口设备

## 安装说明

### 1. 克隆或下载项目
```bash
git clone <repository-url>
cd 气缸控制系统
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置设备
编辑 `config.py` 文件，根据您的硬件配置修改以下参数：

```python
USB_CONFIG = {
    'port': 'COM3',  # 修改为您的串口号
    'baudrate': 9600,
    'timeout': 1,
    # ... 其他配置
}
```

## 使用说明

### 快速启动

#### Windows用户
双击 `run.bat` 文件，选择相应的操作选项。

#### Linux/macOS用户
```bash
chmod +x run.sh
./run.sh
```

### 手动启动

#### 启动主程序
```bash
python main.py
```

#### 启动演示模式
```bash
python demo.py --mode gui      # 图形界面演示
python demo.py --mode console  # 控制台演示
```

#### 运行测试
```bash
python test_system.py --all    # 运行所有测试
python test_system.py --unit   # 只运行单元测试
```

### 操作步骤

1. **连接设备**
   - 启动程序后，点击"连接设备"按钮
   - 确保USB设备已正确连接到指定串口

2. **监控状态**
   - 连接状态：显示USB设备连接状态
   - 气缸状态：显示当前气缸工作状态（空闲/伸出中/已伸出/收回中/已收回）
   - 电路状态：显示电路通断状态（开启/关闭）
   - 传感器状态：显示传感器触发状态（已触发/未触发）

3. **工作模式**
   - **自动模式**：传感器检测到信号时自动启动工作周期
   - **手动模式**：点击"手动启动"按钮启动工作周期

4. **计数管理**
   - 系统自动记录气缸工作周期次数
   - 点击"重置计数"按钮可清零计数器

5. **紧急停止**
   - 点击"紧急停止"按钮立即停止气缸工作并断开电路

### 演示模式

如果您没有实际的硬件设备，可以使用演示模式来体验系统功能：

1. **图形界面演示**：运行 `python demo.py --mode gui`
   - 使用模拟的USB设备和传感器
   - 自动触发传感器演示自动工作模式
   - 所有GUI功能都可正常使用

2. **控制台演示**：运行 `python demo.py --mode console`
   - 在控制台中显示系统工作过程
   - 自动执行多个工作周期
   - 适合了解系统工作流程

## 项目结构

```
气缸控制系统/
├── main.py                 # 主程序入口
├── config.py              # 配置文件
├── logger.py              # 日志记录模块
├── usb_communication.py   # USB通信模块
├── sensor_monitor.py      # 传感器监控模块
├── cylinder_controller.py # 气缸控制核心逻辑
├── gui.py                 # 用户界面模块
├── demo.py                # 演示模式脚本
├── test_system.py         # 系统测试脚本
├── install.py             # 安装脚本
├── hardware_simulator.py  # 硬件模拟器
├── run.bat                # Windows启动脚本
├── run.sh                 # Linux/macOS启动脚本
├── requirements.txt       # 依赖项列表
├── README.md             # 使用说明文档
└── cylinder_control.log  # 系统日志文件（运行时生成）
```

## 配置说明

### USB通信配置
- `port`: 串口号（如 COM3, /dev/ttyUSB0）
- `baudrate`: 波特率（默认9600）
- `timeout`: 超时时间（秒）

### 传感器配置
- `polling_interval`: 传感器轮询间隔（秒）
- `debounce_time`: 防抖时间（秒）
- `trigger_threshold`: 触发阈值

### 气缸工作配置
- `work_cycle_duration`: 工作周期持续时间（秒）
- `extend_time`: 伸出时间（秒）
- `retract_time`: 收回时间（秒）

## 硬件接口协议

系统通过串口发送以下命令与硬件通信：

- `ON\n`: 接通电路
- `OFF\n`: 断开电路
- `READ\n`: 读取传感器状态
- `STATUS\n`: 获取设备状态

硬件应返回相应的状态信息。

## 故障排除

### 常见问题

1. **无法连接USB设备**
   - 检查设备是否正确连接
   - 确认串口号配置正确
   - 检查设备驱动是否已安装

2. **传感器无响应**
   - 检查传感器连接
   - 确认触发阈值设置正确
   - 查看系统日志获取详细信息

3. **气缸不工作**
   - 检查电路连接
   - 确认电磁阀工作正常
   - 检查气压是否充足

### 日志文件
系统会自动生成日志文件 `cylinder_control.log`，包含详细的运行信息和错误记录。

## 技术支持

如有问题，请查看日志文件或联系技术支持。

## 许可证

本项目采用 MIT 许可证。
