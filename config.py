"""
气缸控制系统配置文件
"""

# USB通信配置
USB_CONFIG = {
    'port': 'COM3',  # 默认串口，可根据实际情况修改
    'baudrate': 9600,
    'timeout': 1,
    'bytesize': 8,
    'parity': 'N',
    'stopbits': 1
}

# 传感器配置
SENSOR_CONFIG = {
    'polling_interval': 0.1,  # 传感器轮询间隔（秒）
    'debounce_time': 0.05,    # 防抖时间（秒）
    'trigger_threshold': 1     # 触发阈值
}

# 气缸工作配置
CYLINDER_CONFIG = {
    'work_cycle_duration': 2.0,  # 工作周期持续时间（秒）
    'extend_time': 1.0,          # 伸出时间（秒）
    'retract_time': 1.0          # 收回时间（秒）
}

# GUI配置
GUI_CONFIG = {
    'window_title': '气缸控制系统',
    'window_size': '600x400',
    'update_interval': 100,  # GUI更新间隔（毫秒）
    'font_size': 12
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'filename': 'cylinder_control.log',
    'max_bytes': 1024*1024,  # 1MB
    'backup_count': 5
}

# 控制命令
COMMANDS = {
    'CIRCUIT_ON': b'ON\n',
    'CIRCUIT_OFF': b'OFF\n',
    'READ_SENSOR': b'READ\n',
    'STATUS': b'STATUS\n'
}
