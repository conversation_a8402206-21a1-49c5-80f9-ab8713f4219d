"""
气缸控制核心逻辑模块
"""

import threading
import time
from typing import Callable, Optional
from enum import Enum
from config import CYLINDER_CONFIG
from logger import logger


class CylinderState(Enum):
    """气缸状态枚举"""
    IDLE = "空闲"
    EXTENDING = "伸出中"
    EXTENDED = "已伸出"
    RETRACTING = "收回中"
    RETRACTED = "已收回"


class CylinderController:
    """气缸控制器类"""
    
    def __init__(self, usb_comm):
        self.usb_comm = usb_comm
        self.state = CylinderState.IDLE
        self.cycle_count = 0
        self.is_working = False
        self.work_thread: Optional[threading.Thread] = None
        self.state_change_callback: Optional[Callable] = None
        self.cycle_complete_callback: Optional[Callable] = None
        self.lock = threading.Lock()
        
    def start_work_cycle(self):
        """启动一个工作周期"""
        if self.is_working:
            logger.warning("气缸正在工作中，无法启动新的工作周期")
            return False
        
        with self.lock:
            self.is_working = True
            self.work_thread = threading.Thread(target=self._work_cycle, daemon=True)
            self.work_thread.start()
        
        logger.info("气缸工作周期已启动")
        return True
    
    def _work_cycle(self):
        """执行完整的工作周期"""
        try:
            # 1. 伸出阶段
            self._set_state(CylinderState.EXTENDING)
            self.usb_comm.turn_circuit_on()
            logger.info("气缸开始伸出")
            
            time.sleep(CYLINDER_CONFIG['extend_time'])
            
            # 2. 伸出完成
            self._set_state(CylinderState.EXTENDED)
            logger.info("气缸伸出完成")
            
            # 3. 收回阶段
            self._set_state(CylinderState.RETRACTING)
            logger.info("气缸开始收回")
            
            time.sleep(CYLINDER_CONFIG['retract_time'])
            
            # 4. 收回完成
            self._set_state(CylinderState.RETRACTED)
            self.usb_comm.turn_circuit_off()
            logger.info("气缸收回完成")
            
            # 5. 增加计数
            with self.lock:
                self.cycle_count += 1
                logger.info(f"工作周期完成，当前计数: {self.cycle_count}")
            
            # 6. 回到空闲状态
            self._set_state(CylinderState.IDLE)
            
            # 调用周期完成回调
            if self.cycle_complete_callback:
                try:
                    self.cycle_complete_callback(self.cycle_count)
                except Exception as e:
                    logger.error(f"周期完成回调执行失败: {e}")
                    
        except Exception as e:
            logger.error(f"工作周期执行失败: {e}")
            self._set_state(CylinderState.IDLE)
            self.usb_comm.turn_circuit_off()
        finally:
            with self.lock:
                self.is_working = False
    
    def _set_state(self, new_state: CylinderState):
        """设置气缸状态"""
        old_state = self.state
        self.state = new_state
        logger.debug(f"气缸状态变化: {old_state.value} -> {new_state.value}")
        
        # 调用状态变化回调
        if self.state_change_callback:
            try:
                self.state_change_callback(old_state, new_state)
            except Exception as e:
                logger.error(f"状态变化回调执行失败: {e}")
    
    def emergency_stop(self):
        """紧急停止"""
        logger.warning("执行紧急停止")
        self.usb_comm.turn_circuit_off()
        with self.lock:
            self.is_working = False
        self._set_state(CylinderState.IDLE)
    
    def reset_counter(self):
        """重置计数器"""
        with self.lock:
            old_count = self.cycle_count
            self.cycle_count = 0
        logger.info(f"计数器已重置: {old_count} -> 0")
    
    def get_cycle_count(self) -> int:
        """获取当前计数"""
        with self.lock:
            return self.cycle_count
    
    def get_state(self) -> CylinderState:
        """获取当前状态"""
        return self.state
    
    def is_circuit_on(self) -> bool:
        """检查电路是否接通"""
        status = self.usb_comm.get_status()
        return status == "ON" if status else False
    
    def set_state_change_callback(self, callback: Callable):
        """设置状态变化回调函数"""
        self.state_change_callback = callback
    
    def set_cycle_complete_callback(self, callback: Callable):
        """设置周期完成回调函数"""
        self.cycle_complete_callback = callback
