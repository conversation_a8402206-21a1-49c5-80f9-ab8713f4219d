"""
气缸控制系统演示脚本
"""

import time
import threading
from unittest.mock import Mock
from usb_communication import USBCommunication
from sensor_monitor import SensorMonitor
from cylinder_controller import CylinderController
from gui import CylinderControlGUI
from logger import logger


class MockUSBCommunication:
    """模拟USB通信类，用于演示"""
    
    def __init__(self):
        self.is_connected = False
        self.circuit_on = False
        self.sensor_value = 0
        
    def connect(self, port=None):
        """模拟连接"""
        logger.info("模拟USB设备连接...")
        time.sleep(1)  # 模拟连接延迟
        self.is_connected = True
        logger.info("模拟USB设备连接成功")
        return True
    
    def disconnect(self):
        """模拟断开连接"""
        self.is_connected = False
        self.circuit_on = False
        logger.info("模拟USB设备已断开")
    
    def send_command(self, command):
        """模拟发送命令"""
        if not self.is_connected:
            return False
        
        logger.debug(f"模拟发送命令: {command}")
        return True
    
    def turn_circuit_on(self):
        """模拟接通电路"""
        if self.send_command(b'ON'):
            self.circuit_on = True
            logger.info("模拟电路已接通")
            return True
        return False
    
    def turn_circuit_off(self):
        """模拟断开电路"""
        if self.send_command(b'OFF'):
            self.circuit_on = False
            logger.info("模拟电路已断开")
            return True
        return False
    
    def read_sensor(self):
        """模拟读取传感器"""
        return self.sensor_value
    
    def get_status(self):
        """模拟获取状态"""
        return "ON" if self.circuit_on else "OFF"
    
    def trigger_sensor(self):
        """模拟触发传感器"""
        self.sensor_value = 1
        logger.info("模拟传感器触发")
        
        # 0.5秒后自动复位
        def reset():
            time.sleep(0.5)
            self.sensor_value = 0
            logger.debug("模拟传感器复位")
        
        threading.Thread(target=reset, daemon=True).start()


class DemoController:
    """演示控制器"""
    
    def __init__(self):
        self.mock_usb = MockUSBCommunication()
        self.controller = CylinderController(self.mock_usb)
        self.sensor_monitor = SensorMonitor(self.mock_usb)
        self.gui = None
        self.auto_demo_running = False
        
    def start_gui_demo(self):
        """启动GUI演示"""
        logger.info("启动GUI演示模式")
        
        # 创建GUI
        self.gui = CylinderControlGUI(self.controller, self.sensor_monitor, self.mock_usb)
        
        # 设置回调
        self.sensor_monitor.set_trigger_callback(self._on_sensor_trigger)
        
        # 启动自动演示
        self._start_auto_demo()
        
        # 运行GUI
        self.gui.run()
    
    def start_console_demo(self):
        """启动控制台演示"""
        logger.info("启动控制台演示模式")
        
        try:
            # 模拟连接设备
            print("正在连接模拟设备...")
            self.mock_usb.connect()
            
            # 启动传感器监控
            self.sensor_monitor.set_trigger_callback(self._on_sensor_trigger)
            self.sensor_monitor.start_monitoring()
            
            print("演示开始，按Ctrl+C退出")
            print("-" * 40)
            
            # 演示循环
            for i in range(5):
                print(f"\n第 {i+1} 次演示:")
                
                # 手动启动工作周期
                print("手动启动工作周期...")
                if self.controller.start_work_cycle():
                    # 等待工作周期完成
                    while self.controller.is_working:
                        print(f"气缸状态: {self.controller.get_state().value}")
                        time.sleep(0.5)
                    
                    print(f"工作周期完成，当前计数: {self.controller.get_cycle_count()}")
                
                time.sleep(2)
                
                # 模拟传感器触发
                print("模拟传感器触发...")
                self.mock_usb.trigger_sensor()
                
                # 等待自动工作周期完成
                time.sleep(3)
                print(f"自动工作周期完成，当前计数: {self.controller.get_cycle_count()}")
                
                time.sleep(2)
            
            print("\n演示完成！")
            print(f"总工作次数: {self.controller.get_cycle_count()}")
            
        except KeyboardInterrupt:
            print("\n用户中断演示")
        finally:
            self._cleanup()
    
    def _on_sensor_trigger(self):
        """传感器触发回调"""
        logger.info("演示：传感器触发，启动自动工作周期")
        self.controller.start_work_cycle()
    
    def _start_auto_demo(self):
        """启动自动演示"""
        if not self.auto_demo_running:
            self.auto_demo_running = True
            demo_thread = threading.Thread(target=self._auto_demo_loop, daemon=True)
            demo_thread.start()
    
    def _auto_demo_loop(self):
        """自动演示循环"""
        time.sleep(5)  # 等待GUI启动
        
        while self.auto_demo_running:
            try:
                # 每10-20秒触发一次传感器
                time.sleep(15)
                
                if self.auto_demo_running and self.mock_usb.is_connected:
                    logger.info("自动演示：触发传感器")
                    self.mock_usb.trigger_sensor()
                    
            except Exception as e:
                logger.error(f"自动演示循环错误: {e}")
                break
    
    def _cleanup(self):
        """清理资源"""
        self.auto_demo_running = False
        self.sensor_monitor.stop_monitoring()
        self.mock_usb.disconnect()
        logger.info("演示资源清理完成")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='气缸控制系统演示')
    parser.add_argument('--mode', choices=['gui', 'console'], default='gui',
                       help='演示模式: gui(图形界面) 或 console(控制台)')
    
    args = parser.parse_args()
    
    demo = DemoController()
    
    try:
        if args.mode == 'gui':
            demo.start_gui_demo()
        else:
            demo.start_console_demo()
    except Exception as e:
        logger.error(f"演示运行异常: {e}")
    finally:
        demo._cleanup()


if __name__ == "__main__":
    main()
