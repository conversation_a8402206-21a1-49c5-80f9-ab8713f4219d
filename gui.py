"""
用户界面模块
"""

import tkinter as tk
from tkinter import ttk, messagebox
from config import GUI_CONFIG
from logger import logger
from cylinder_controller import CylinderState


class CylinderControlGUI:
    """气缸控制系统GUI类"""
    
    def __init__(self, controller, sensor_monitor, usb_comm):
        self.controller = controller
        self.sensor_monitor = sensor_monitor
        self.usb_comm = usb_comm
        
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title(GUI_CONFIG['window_title'])
        self.root.geometry(GUI_CONFIG['window_size'])
        self.root.resizable(False, False)
        
        # 设置样式
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # 创建界面元素
        self._create_widgets()
        
        # 设置回调函数
        self._setup_callbacks()
        
        # 启动GUI更新循环
        self._start_update_loop()
        
        logger.info("GUI界面已初始化")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="气缸控制系统", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 连接状态框架
        conn_frame = ttk.LabelFrame(main_frame, text="连接状态", padding="10")
        conn_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.conn_status_label = ttk.Label(conn_frame, text="未连接", 
                                          foreground="red")
        self.conn_status_label.grid(row=0, column=0, sticky=tk.W)
        
        self.connect_btn = ttk.Button(conn_frame, text="连接设备", 
                                     command=self._connect_device)
        self.connect_btn.grid(row=0, column=1, padx=(10, 0))
        
        # 状态显示框架
        status_frame = ttk.LabelFrame(main_frame, text="系统状态", padding="10")
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 气缸状态
        ttk.Label(status_frame, text="气缸状态:").grid(row=0, column=0, sticky=tk.W)
        self.cylinder_status_label = ttk.Label(status_frame, text="空闲", 
                                              foreground="blue")
        self.cylinder_status_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # 电路状态
        ttk.Label(status_frame, text="电路状态:").grid(row=1, column=0, sticky=tk.W)
        self.circuit_status_label = ttk.Label(status_frame, text="关闭", 
                                             foreground="red")
        self.circuit_status_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # 传感器状态
        ttk.Label(status_frame, text="传感器状态:").grid(row=2, column=0, sticky=tk.W)
        self.sensor_status_label = ttk.Label(status_frame, text="未触发", 
                                            foreground="gray")
        self.sensor_status_label.grid(row=2, column=1, sticky=tk.W, padx=(10, 0))
        
        # 计数器框架
        counter_frame = ttk.LabelFrame(main_frame, text="工作计数", padding="10")
        counter_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(counter_frame, text="累计次数:").grid(row=0, column=0, sticky=tk.W)
        self.counter_label = ttk.Label(counter_frame, text="0", 
                                      font=("Arial", 14, "bold"), 
                                      foreground="green")
        self.counter_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # 控制按钮框架
        control_frame = ttk.LabelFrame(main_frame, text="控制操作", padding="10")
        control_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.manual_start_btn = ttk.Button(control_frame, text="手动启动", 
                                          command=self._manual_start)
        self.manual_start_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.reset_btn = ttk.Button(control_frame, text="重置计数", 
                                   command=self._reset_counter)
        self.reset_btn.grid(row=0, column=1, padx=(0, 10))
        
        self.emergency_btn = ttk.Button(control_frame, text="紧急停止", 
                                       command=self._emergency_stop)
        self.emergency_btn.grid(row=0, column=2)
        
        # 日志框架
        log_frame = ttk.LabelFrame(main_frame, text="系统日志", padding="10")
        log_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 日志文本框
        self.log_text = tk.Text(log_frame, height=8, width=60)
        log_scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置网格权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(5, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
    
    def _setup_callbacks(self):
        """设置回调函数"""
        # 设置气缸控制器回调
        self.controller.set_state_change_callback(self._on_cylinder_state_change)
        self.controller.set_cycle_complete_callback(self._on_cycle_complete)
        
        # 设置传感器监控回调
        self.sensor_monitor.set_trigger_callback(self._on_sensor_trigger)
    
    def _start_update_loop(self):
        """启动GUI更新循环"""
        self._update_gui()
    
    def _update_gui(self):
        """更新GUI显示"""
        try:
            # 更新连接状态
            if self.usb_comm.is_connected:
                self.conn_status_label.config(text="已连接", foreground="green")
                self.connect_btn.config(text="断开连接")
            else:
                self.conn_status_label.config(text="未连接", foreground="red")
                self.connect_btn.config(text="连接设备")
            
            # 更新电路状态
            if self.usb_comm.is_connected:
                circuit_on = self.controller.is_circuit_on()
                if circuit_on:
                    self.circuit_status_label.config(text="开启", foreground="green")
                else:
                    self.circuit_status_label.config(text="关闭", foreground="red")
            
            # 更新传感器状态
            if self.usb_comm.is_connected:
                sensor_triggered = self.sensor_monitor.is_sensor_triggered()
                if sensor_triggered:
                    self.sensor_status_label.config(text="已触发", foreground="orange")
                else:
                    self.sensor_status_label.config(text="未触发", foreground="gray")
            
            # 更新计数器
            count = self.controller.get_cycle_count()
            self.counter_label.config(text=str(count))
            
        except Exception as e:
            logger.error(f"GUI更新失败: {e}")
        
        # 安排下次更新
        self.root.after(GUI_CONFIG['update_interval'], self._update_gui)

    def _connect_device(self):
        """连接/断开设备"""
        if self.usb_comm.is_connected:
            self._disconnect_device()
        else:
            self._connect_to_device()

    def _connect_to_device(self):
        """连接到设备"""
        try:
            if self.usb_comm.connect():
                self.sensor_monitor.start_monitoring()
                self._log_message("设备连接成功")
                messagebox.showinfo("连接成功", "USB设备连接成功！")
            else:
                self._log_message("设备连接失败")
                messagebox.showerror("连接失败", "无法连接到USB设备，请检查设备连接和端口配置。")
        except Exception as e:
            self._log_message(f"连接异常: {e}")
            messagebox.showerror("连接异常", f"连接过程中发生异常：{e}")

    def _disconnect_device(self):
        """断开设备连接"""
        try:
            self.sensor_monitor.stop_monitoring()
            self.usb_comm.disconnect()
            self._log_message("设备已断开连接")
        except Exception as e:
            self._log_message(f"断开连接异常: {e}")

    def _manual_start(self):
        """手动启动工作周期"""
        if not self.usb_comm.is_connected:
            messagebox.showwarning("设备未连接", "请先连接USB设备！")
            return

        if self.controller.start_work_cycle():
            self._log_message("手动启动工作周期")
        else:
            messagebox.showwarning("无法启动", "气缸正在工作中，请等待当前周期完成。")

    def _reset_counter(self):
        """重置计数器"""
        result = messagebox.askyesno("确认重置", "确定要重置计数器吗？")
        if result:
            self.controller.reset_counter()
            self._log_message("计数器已重置")

    def _emergency_stop(self):
        """紧急停止"""
        result = messagebox.askyesno("确认停止", "确定要执行紧急停止吗？")
        if result:
            self.controller.emergency_stop()
            self._log_message("执行紧急停止")

    def _on_cylinder_state_change(self, old_state, new_state):
        """气缸状态变化回调"""
        # old_state 参数保留用于日志记录或其他扩展功能
        def update():
            self.cylinder_status_label.config(text=new_state.value)
            if new_state == CylinderState.IDLE:
                self.cylinder_status_label.config(foreground="blue")
            elif new_state in [CylinderState.EXTENDING, CylinderState.RETRACTING]:
                self.cylinder_status_label.config(foreground="orange")
            else:
                self.cylinder_status_label.config(foreground="green")

        # 在主线程中更新GUI
        self.root.after(0, update)

    def _on_cycle_complete(self, count):
        """工作周期完成回调"""
        def update():
            self._log_message(f"工作周期完成，当前计数: {count}")

        self.root.after(0, update)

    def _on_sensor_trigger(self):
        """传感器触发回调"""
        def update():
            self._log_message("传感器触发，启动工作周期")
            if not self.controller.start_work_cycle():
                self._log_message("工作周期启动失败：气缸正在工作中")

        self.root.after(0, update)

    def _log_message(self, message):
        """在日志框中显示消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

        # 限制日志行数
        lines = int(self.log_text.index('end-1c').split('.')[0])
        if lines > 100:
            self.log_text.delete('1.0', '10.0')

    def run(self):
        """运行GUI主循环"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            logger.info("用户中断程序")
        finally:
            self._cleanup()

    def _cleanup(self):
        """清理资源"""
        try:
            self.sensor_monitor.stop_monitoring()
            self.usb_comm.disconnect()
            logger.info("GUI资源清理完成")
        except Exception as e:
            logger.error(f"GUI资源清理失败: {e}")
