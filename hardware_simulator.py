"""
硬件模拟器 - 用于测试气缸控制系统
"""

import socket
import threading
import time
import random
from logger import logger


class HardwareSimulator:
    """硬件模拟器类"""
    
    def __init__(self, host='localhost', port=8888):
        self.host = host
        self.port = port
        self.server_socket = None
        self.client_socket = None
        self.is_running = False
        self.circuit_on = False
        self.sensor_value = 0
        self.auto_trigger = False
        
        # 模拟传感器自动触发
        self.trigger_thread = None
        
    def start_server(self):
        """启动模拟器服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(1)
            
            self.is_running = True
            logger.info(f"硬件模拟器服务器启动，监听 {self.host}:{self.port}")
            
            # 启动自动触发线程
            if self.auto_trigger:
                self.trigger_thread = threading.Thread(target=self._auto_trigger_loop, daemon=True)
                self.trigger_thread.start()
            
            while self.is_running:
                try:
                    logger.info("等待客户端连接...")
                    self.client_socket, addr = self.server_socket.accept()
                    logger.info(f"客户端已连接: {addr}")
                    
                    # 处理客户端请求
                    self._handle_client()
                    
                except socket.error as e:
                    if self.is_running:
                        logger.error(f"服务器套接字错误: {e}")
                    break
                    
        except Exception as e:
            logger.error(f"启动服务器失败: {e}")
        finally:
            self.stop_server()
    
    def _handle_client(self):
        """处理客户端请求"""
        try:
            while self.is_running and self.client_socket:
                data = self.client_socket.recv(1024)
                if not data:
                    break
                
                command = data.decode('utf-8').strip()
                logger.debug(f"接收到命令: {command}")
                
                response = self._process_command(command)
                if response:
                    self.client_socket.send(response.encode('utf-8'))
                    
        except socket.error as e:
            logger.error(f"客户端通信错误: {e}")
        finally:
            if self.client_socket:
                self.client_socket.close()
                self.client_socket = None
                logger.info("客户端连接已关闭")
    
    def _process_command(self, command):
        """处理命令并返回响应"""
        if command == "ON":
            self.circuit_on = True
            logger.info("电路已接通")
            return "OK\n"
        
        elif command == "OFF":
            self.circuit_on = False
            logger.info("电路已断开")
            return "OK\n"
        
        elif command == "READ":
            # 返回当前传感器值
            return f"{self.sensor_value}\n"
        
        elif command == "STATUS":
            # 返回电路状态
            status = "ON" if self.circuit_on else "OFF"
            return f"{status}\n"
        
        else:
            logger.warning(f"未知命令: {command}")
            return "ERROR\n"
    
    def _auto_trigger_loop(self):
        """自动触发传感器的循环"""
        while self.is_running and self.auto_trigger:
            try:
                # 随机间隔触发传感器
                time.sleep(random.uniform(5, 15))
                
                if self.is_running:
                    self.trigger_sensor()
                    
            except Exception as e:
                logger.error(f"自动触发循环错误: {e}")
    
    def trigger_sensor(self):
        """触发传感器"""
        self.sensor_value = 1
        logger.info("传感器已触发")
        
        # 0.5秒后自动复位
        def reset_sensor():
            time.sleep(0.5)
            self.sensor_value = 0
            logger.debug("传感器已复位")
        
        reset_thread = threading.Thread(target=reset_sensor, daemon=True)
        reset_thread.start()
    
    def set_auto_trigger(self, enabled):
        """设置自动触发模式"""
        self.auto_trigger = enabled
        if enabled and self.is_running and not self.trigger_thread:
            self.trigger_thread = threading.Thread(target=self._auto_trigger_loop, daemon=True)
            self.trigger_thread.start()
            logger.info("自动触发模式已启用")
        else:
            logger.info("自动触发模式已禁用")
    
    def stop_server(self):
        """停止服务器"""
        self.is_running = False
        self.auto_trigger = False
        
        if self.client_socket:
            self.client_socket.close()
        
        if self.server_socket:
            self.server_socket.close()
        
        logger.info("硬件模拟器服务器已停止")


def main():
    """模拟器主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='气缸控制系统硬件模拟器')
    parser.add_argument('--host', default='localhost', help='服务器主机地址')
    parser.add_argument('--port', type=int, default=8888, help='服务器端口')
    parser.add_argument('--auto-trigger', action='store_true', help='启用自动触发模式')
    
    args = parser.parse_args()
    
    simulator = HardwareSimulator(args.host, args.port)
    simulator.set_auto_trigger(args.auto_trigger)
    
    try:
        simulator.start_server()
    except KeyboardInterrupt:
        logger.info("用户中断模拟器")
    finally:
        simulator.stop_server()


if __name__ == "__main__":
    main()
