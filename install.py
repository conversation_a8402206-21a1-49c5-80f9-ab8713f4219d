"""
气缸控制系统安装脚本
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True


def install_dependencies():
    """安装依赖包"""
    print("正在安装依赖包...")
    
    try:
        # 升级pip
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # 安装requirements.txt中的依赖
        requirements_file = Path("requirements.txt")
        if requirements_file.exists():
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print("依赖包安装完成")
            return True
        else:
            print("错误: 找不到requirements.txt文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"安装依赖包失败: {e}")
        return False


def check_serial_ports():
    """检查可用的串口"""
    print("检查可用串口...")
    
    try:
        import serial.tools.list_ports
        
        ports = list(serial.tools.list_ports.comports())
        if ports:
            print("可用串口:")
            for port in ports:
                print(f"  - {port.device}: {port.description}")
        else:
            print("警告: 未找到可用串口")
        
        return True
        
    except ImportError:
        print("警告: 无法检查串口（pyserial未安装）")
        return False


def create_desktop_shortcut():
    """创建桌面快捷方式"""
    system = platform.system()
    
    if system == "Windows":
        return create_windows_shortcut()
    elif system == "Linux":
        return create_linux_shortcut()
    elif system == "Darwin":  # macOS
        return create_macos_shortcut()
    else:
        print(f"不支持的操作系统: {system}")
        return False


def create_windows_shortcut():
    """创建Windows快捷方式"""
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "气缸控制系统.lnk")
        target = os.path.join(os.getcwd(), "main.py")
        wDir = os.getcwd()
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{target}"'
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        print(f"桌面快捷方式已创建: {path}")
        return True
        
    except ImportError:
        print("警告: 无法创建Windows快捷方式（需要pywin32和winshell）")
        return False
    except Exception as e:
        print(f"创建Windows快捷方式失败: {e}")
        return False


def create_linux_shortcut():
    """创建Linux桌面文件"""
    try:
        desktop_dir = Path.home() / "Desktop"
        if not desktop_dir.exists():
            desktop_dir = Path.home() / "桌面"
        
        if desktop_dir.exists():
            desktop_file = desktop_dir / "气缸控制系统.desktop"
            
            content = f"""[Desktop Entry]
Name=气缸控制系统
Comment=Cylinder Control System
Exec={sys.executable} "{os.path.join(os.getcwd(), 'main.py')}"
Icon=applications-engineering
Terminal=false
Type=Application
Categories=Engineering;
"""
            
            with open(desktop_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 设置可执行权限
            os.chmod(desktop_file, 0o755)
            
            print(f"桌面快捷方式已创建: {desktop_file}")
            return True
        else:
            print("警告: 找不到桌面目录")
            return False
            
    except Exception as e:
        print(f"创建Linux快捷方式失败: {e}")
        return False


def create_macos_shortcut():
    """创建macOS应用程序"""
    print("macOS快捷方式创建功能尚未实现")
    return False


def run_tests():
    """运行测试"""
    print("运行系统测试...")
    
    try:
        # 运行单元测试
        result = subprocess.run([sys.executable, "test_system.py", "--unit"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("测试通过")
            return True
        else:
            print("测试失败:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"运行测试失败: {e}")
        return False


def main():
    """主安装函数"""
    print("=" * 50)
    print("气缸控制系统安装程序")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 安装依赖
    if not install_dependencies():
        print("安装失败: 无法安装依赖包")
        sys.exit(1)
    
    # 检查串口
    check_serial_ports()
    
    # 运行测试
    if not run_tests():
        print("警告: 测试未通过，但安装将继续")
    
    # 创建快捷方式
    create_desktop_shortcut()
    
    print("\n" + "=" * 50)
    print("安装完成！")
    print("=" * 50)
    print("\n使用说明:")
    print("1. 运行程序: python main.py")
    print("2. 或者双击桌面快捷方式（如果已创建）")
    print("3. 首次使用前请在config.py中配置正确的串口号")
    print("4. 查看README.md获取详细使用说明")
    print("\n如有问题，请查看日志文件: cylinder_control.log")


if __name__ == "__main__":
    main()
