"""
日志记录模块
"""

import logging
import logging.handlers
from config import LOG_CONFIG


class Logger:
    """日志记录器类"""
    
    def __init__(self, name='CylinderControl'):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, LOG_CONFIG['level']))
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _setup_handlers(self):
        """设置日志处理器"""
        # 文件处理器（带轮转）
        file_handler = logging.handlers.RotatingFileHandler(
            LOG_CONFIG['filename'],
            maxBytes=LOG_CONFIG['max_bytes'],
            backupCount=LOG_CONFIG['backup_count'],
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter(LOG_CONFIG['format'])
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def debug(self, message):
        """调试信息"""
        self.logger.debug(message)
    
    def info(self, message):
        """一般信息"""
        self.logger.info(message)
    
    def warning(self, message):
        """警告信息"""
        self.logger.warning(message)
    
    def error(self, message):
        """错误信息"""
        self.logger.error(message)
    
    def critical(self, message):
        """严重错误"""
        self.logger.critical(message)


# 创建全局日志实例
logger = Logger()
