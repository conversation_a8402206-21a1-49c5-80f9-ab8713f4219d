"""
气缸控制系统主程序
"""

import sys
import signal
from logger import logger
from usb_communication import USBCommunication
from sensor_monitor import SensorMonitor
from cylinder_controller import CylinderController
from gui import CylinderControlGUI


class CylinderControlSystem:
    """气缸控制系统主类"""
    
    def __init__(self):
        self.usb_comm = None
        self.sensor_monitor = None
        self.controller = None
        self.gui = None
        
    def initialize(self):
        """初始化系统组件"""
        try:
            logger.info("正在初始化气缸控制系统...")
            
            # 初始化USB通信
            self.usb_comm = USBCommunication()
            logger.info("USB通信模块已初始化")
            
            # 初始化气缸控制器
            self.controller = CylinderController(self.usb_comm)
            logger.info("气缸控制器已初始化")
            
            # 初始化传感器监控
            self.sensor_monitor = SensorMonitor(self.usb_comm)
            logger.info("传感器监控模块已初始化")
            
            # 初始化GUI
            self.gui = CylinderControlGUI(self.controller, self.sensor_monitor, self.usb_comm)
            logger.info("GUI界面已初始化")
            
            logger.info("系统初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"系统初始化失败: {e}")
            return False
    
    def run(self):
        """运行系统"""
        if not self.initialize():
            logger.error("系统初始化失败，程序退出")
            return False
        
        try:
            logger.info("启动气缸控制系统")
            
            # 设置信号处理器
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            # 运行GUI主循环
            self.gui.run()
            
        except Exception as e:
            logger.error(f"系统运行异常: {e}")
            return False
        finally:
            self.cleanup()
        
        return True
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        # frame 参数由信号处理器要求，保留用于兼容性
        logger.info(f"接收到信号 {signum}，正在关闭系统...")
        self.cleanup()
        sys.exit(0)
    
    def cleanup(self):
        """清理系统资源"""
        try:
            logger.info("正在清理系统资源...")
            
            if self.sensor_monitor:
                self.sensor_monitor.stop_monitoring()
                logger.info("传感器监控已停止")
            
            if self.controller:
                self.controller.emergency_stop()
                logger.info("气缸控制器已停止")
            
            if self.usb_comm:
                self.usb_comm.disconnect()
                logger.info("USB连接已断开")
            
            logger.info("系统资源清理完成")
            
        except Exception as e:
            logger.error(f"系统资源清理失败: {e}")


def main():
    """主函数"""
    try:
        logger.info("=" * 50)
        logger.info("气缸控制系统启动")
        logger.info("=" * 50)
        
        # 创建并运行系统
        system = CylinderControlSystem()
        success = system.run()
        
        if success:
            logger.info("系统正常退出")
        else:
            logger.error("系统异常退出")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
