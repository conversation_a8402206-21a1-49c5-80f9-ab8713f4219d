@echo off
chcp 65001 >nul
title 气缸控制系统

echo ========================================
echo           气缸控制系统
echo ========================================
echo.

:menu
echo 请选择操作:
echo 1. 运行主程序
echo 2. 运行演示模式
echo 3. 运行测试
echo 4. 安装依赖
echo 5. 退出
echo.
set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" goto run_main
if "%choice%"=="2" goto run_demo
if "%choice%"=="3" goto run_test
if "%choice%"=="4" goto install
if "%choice%"=="5" goto exit
echo 无效选择，请重新输入
echo.
goto menu

:run_main
echo.
echo 启动主程序...
python main.py
pause
goto menu

:run_demo
echo.
echo 启动演示模式...
python demo.py --mode gui
pause
goto menu

:run_test
echo.
echo 运行系统测试...
python test_system.py --all
pause
goto menu

:install
echo.
echo 安装依赖包...
python install.py
pause
goto menu

:exit
echo.
echo 感谢使用气缸控制系统！
pause
exit
