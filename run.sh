#!/bin/bash

# 气缸控制系统启动脚本

echo "========================================"
echo "           气缸控制系统"
echo "========================================"
echo

show_menu() {
    echo "请选择操作:"
    echo "1. 运行主程序"
    echo "2. 运行演示模式"
    echo "3. 运行测试"
    echo "4. 安装依赖"
    echo "5. 退出"
    echo
}

run_main() {
    echo
    echo "启动主程序..."
    python3 main.py
    echo
    read -p "按回车键继续..."
}

run_demo() {
    echo
    echo "启动演示模式..."
    python3 demo.py --mode gui
    echo
    read -p "按回车键继续..."
}

run_test() {
    echo
    echo "运行系统测试..."
    python3 test_system.py --all
    echo
    read -p "按回车键继续..."
}

install_deps() {
    echo
    echo "安装依赖包..."
    python3 install.py
    echo
    read -p "按回车键继续..."
}

while true; do
    show_menu
    read -p "请输入选择 (1-5): " choice
    
    case $choice in
        1)
            run_main
            ;;
        2)
            run_demo
            ;;
        3)
            run_test
            ;;
        4)
            install_deps
            ;;
        5)
            echo
            echo "感谢使用气缸控制系统！"
            exit 0
            ;;
        *)
            echo "无效选择，请重新输入"
            echo
            ;;
    esac
done
