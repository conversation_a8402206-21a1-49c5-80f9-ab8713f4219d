"""
传感器监控模块
"""

import threading
import time
from typing import Callable, Optional
from config import SENSOR_CONFIG
from logger import logger


class SensorMonitor:
    """传感器监控类"""
    
    def __init__(self, usb_comm, trigger_callback: Callable = None):
        self.usb_comm = usb_comm
        self.trigger_callback = trigger_callback
        self.is_monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.last_sensor_value = 0
        self.last_trigger_time = 0
        
    def start_monitoring(self):
        """开始监控传感器"""
        if self.is_monitoring:
            logger.warning("传感器监控已在运行")
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("传感器监控已启动")
    
    def stop_monitoring(self):
        """停止监控传感器"""
        self.is_monitoring = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=1.0)
        logger.info("传感器监控已停止")
    
    def _monitor_loop(self):
        """传感器监控循环"""
        while self.is_monitoring:
            try:
                # 读取传感器值
                sensor_value = self.usb_comm.read_sensor()
                
                if sensor_value is not None:
                    # 检测信号变化（从低到高的跳变）
                    if (sensor_value >= SENSOR_CONFIG['trigger_threshold'] and 
                        self.last_sensor_value < SENSOR_CONFIG['trigger_threshold']):
                        
                        current_time = time.time()
                        
                        # 防抖处理
                        if (current_time - self.last_trigger_time) > SENSOR_CONFIG['debounce_time']:
                            logger.info(f"传感器触发检测到，值: {sensor_value}")
                            self.last_trigger_time = current_time
                            
                            # 调用触发回调函数
                            if self.trigger_callback:
                                try:
                                    self.trigger_callback()
                                except Exception as e:
                                    logger.error(f"传感器触发回调执行失败: {e}")
                    
                    self.last_sensor_value = sensor_value
                
                # 等待下次轮询
                time.sleep(SENSOR_CONFIG['polling_interval'])
                
            except Exception as e:
                logger.error(f"传感器监控循环出错: {e}")
                time.sleep(SENSOR_CONFIG['polling_interval'])
    
    def get_current_value(self) -> Optional[int]:
        """获取当前传感器值"""
        return self.usb_comm.read_sensor()
    
    def set_trigger_callback(self, callback: Callable):
        """设置触发回调函数"""
        self.trigger_callback = callback
        logger.debug("传感器触发回调函数已设置")
    
    def is_sensor_triggered(self) -> bool:
        """检查传感器是否被触发"""
        current_value = self.get_current_value()
        if current_value is not None:
            return current_value >= SENSOR_CONFIG['trigger_threshold']
        return False
