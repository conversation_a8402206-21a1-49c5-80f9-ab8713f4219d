"""
气缸控制系统测试脚本
"""

import unittest
import time
from unittest.mock import Mock, patch
from usb_communication import USBCommunication
from sensor_monitor import SensorMonitor
from cylinder_controller import <PERSON><PERSON>erController, CylinderState
from logger import logger


class TestUSBCommunication(unittest.TestCase):
    """USB通信模块测试"""
    
    def setUp(self):
        self.usb_comm = USBCommunication()
    
    def test_initial_state(self):
        """测试初始状态"""
        self.assertFalse(self.usb_comm.is_connected)
        self.assertIsNone(self.usb_comm.serial_port)
    
    @patch('serial.Serial')
    def test_connect_success(self, mock_serial):
        """测试连接成功"""
        mock_port = Mock()
        mock_serial.return_value = mock_port
        
        result = self.usb_comm.connect('COM3')
        
        self.assertTrue(result)
        self.assertTrue(self.usb_comm.is_connected)
        mock_serial.assert_called_once()
    
    @patch('serial.Serial')
    def test_connect_failure(self, mock_serial):
        """测试连接失败"""
        mock_serial.side_effect = Exception("连接失败")
        
        result = self.usb_comm.connect('COM3')
        
        self.assertFalse(result)
        self.assertFalse(self.usb_comm.is_connected)
    
    def test_send_command_not_connected(self):
        """测试未连接时发送命令"""
        result = self.usb_comm.send_command(b'TEST')
        self.assertFalse(result)


class TestCylinderController(unittest.TestCase):
    """气缸控制器测试"""
    
    def setUp(self):
        self.mock_usb = Mock()
        self.controller = CylinderController(self.mock_usb)
    
    def test_initial_state(self):
        """测试初始状态"""
        self.assertEqual(self.controller.get_state(), CylinderState.IDLE)
        self.assertEqual(self.controller.get_cycle_count(), 0)
        self.assertFalse(self.controller.is_working)
    
    def test_reset_counter(self):
        """测试重置计数器"""
        # 模拟增加计数
        self.controller.cycle_count = 5
        
        self.controller.reset_counter()
        
        self.assertEqual(self.controller.get_cycle_count(), 0)
    
    def test_emergency_stop(self):
        """测试紧急停止"""
        self.controller.is_working = True
        
        self.controller.emergency_stop()
        
        self.assertFalse(self.controller.is_working)
        self.assertEqual(self.controller.get_state(), CylinderState.IDLE)
        self.mock_usb.turn_circuit_off.assert_called_once()
    
    def test_work_cycle_when_busy(self):
        """测试忙碌时启动工作周期"""
        self.controller.is_working = True
        
        result = self.controller.start_work_cycle()
        
        self.assertFalse(result)


class TestSensorMonitor(unittest.TestCase):
    """传感器监控测试"""
    
    def setUp(self):
        self.mock_usb = Mock()
        self.mock_callback = Mock()
        self.sensor_monitor = SensorMonitor(self.mock_usb, self.mock_callback)
    
    def test_initial_state(self):
        """测试初始状态"""
        self.assertFalse(self.sensor_monitor.is_monitoring)
        self.assertIsNone(self.sensor_monitor.monitor_thread)
    
    def test_set_trigger_callback(self):
        """测试设置触发回调"""
        new_callback = Mock()
        
        self.sensor_monitor.set_trigger_callback(new_callback)
        
        self.assertEqual(self.sensor_monitor.trigger_callback, new_callback)
    
    def test_get_current_value(self):
        """测试获取当前传感器值"""
        self.mock_usb.read_sensor.return_value = 1
        
        value = self.sensor_monitor.get_current_value()
        
        self.assertEqual(value, 1)
        self.mock_usb.read_sensor.assert_called_once()


class TestSystemIntegration(unittest.TestCase):
    """系统集成测试"""
    
    def setUp(self):
        self.mock_usb = Mock()
        self.mock_usb.is_connected = True
        self.controller = CylinderController(self.mock_usb)
        self.sensor_monitor = SensorMonitor(self.mock_usb)
    
    def test_sensor_trigger_starts_cycle(self):
        """测试传感器触发启动工作周期"""
        # 设置传感器监控回调
        self.sensor_monitor.set_trigger_callback(self.controller.start_work_cycle)
        
        # 模拟传感器触发
        self.mock_usb.read_sensor.return_value = 1
        
        # 手动调用触发回调
        if self.sensor_monitor.trigger_callback:
            self.sensor_monitor.trigger_callback()
        
        # 等待工作线程启动
        time.sleep(0.1)
        
        # 验证工作周期已启动
        self.assertTrue(self.controller.is_working)


class TestPerformance(unittest.TestCase):
    """性能测试"""
    
    def test_multiple_rapid_triggers(self):
        """测试快速多次触发"""
        mock_usb = Mock()
        mock_usb.is_connected = True
        controller = CylinderController(mock_usb)
        
        # 快速启动多个工作周期
        results = []
        for i in range(10):
            result = controller.start_work_cycle()
            results.append(result)
            time.sleep(0.01)  # 很短的间隔
        
        # 只有第一个应该成功
        self.assertTrue(results[0])
        self.assertTrue(all(not r for r in results[1:]))
    
    def test_sensor_polling_performance(self):
        """测试传感器轮询性能"""
        mock_usb = Mock()
        mock_usb.read_sensor.return_value = 0
        
        sensor_monitor = SensorMonitor(mock_usb)
        
        start_time = time.time()
        
        # 模拟100次传感器读取
        for _ in range(100):
            sensor_monitor.get_current_value()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 应该在合理时间内完成
        self.assertLess(duration, 1.0)  # 1秒内完成100次读取


def run_functional_test():
    """运行功能测试"""
    logger.info("开始功能测试...")
    
    # 创建模拟USB通信
    mock_usb = Mock()
    mock_usb.is_connected = True
    mock_usb.turn_circuit_on.return_value = True
    mock_usb.turn_circuit_off.return_value = True
    mock_usb.read_sensor.return_value = 0
    mock_usb.get_status.return_value = "OFF"
    
    # 创建控制器
    controller = CylinderController(mock_usb)
    
    # 测试工作周期
    logger.info("测试工作周期...")
    success = controller.start_work_cycle()
    if success:
        logger.info("工作周期启动成功")
        
        # 等待工作周期完成
        time.sleep(3)
        
        # 检查计数
        count = controller.get_cycle_count()
        logger.info(f"工作周期完成，计数: {count}")
        
        if count > 0:
            logger.info("功能测试通过")
            return True
    
    logger.error("功能测试失败")
    return False


def main():
    """主测试函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='气缸控制系统测试')
    parser.add_argument('--unit', action='store_true', help='运行单元测试')
    parser.add_argument('--functional', action='store_true', help='运行功能测试')
    parser.add_argument('--all', action='store_true', help='运行所有测试')
    
    args = parser.parse_args()
    
    if args.all or args.unit:
        logger.info("运行单元测试...")
        unittest.main(argv=[''], exit=False, verbosity=2)
    
    if args.all or args.functional:
        run_functional_test()
    
    if not any([args.unit, args.functional, args.all]):
        logger.info("请指定测试类型: --unit, --functional, 或 --all")


if __name__ == "__main__":
    main()
