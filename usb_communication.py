"""
USB通信模块
"""

import serial
import time
import threading
from typing import Optional, Callable
from config import USB_CONFIG, COMMANDS
from logger import logger


class USBCommunication:
    """USB通信类"""
    
    def __init__(self):
        self.serial_port: Optional[serial.Serial] = None
        self.is_connected = False
        self.lock = threading.Lock()
        
    def connect(self, port: str = None) -> bool:
        """连接USB设备"""
        try:
            port = port or USB_CONFIG['port']
            self.serial_port = serial.Serial(
                port=port,
                baudrate=USB_CONFIG['baudrate'],
                timeout=USB_CONFIG['timeout'],
                bytesize=USB_CONFIG['bytesize'],
                parity=USB_CONFIG['parity'],
                stopbits=USB_CONFIG['stopbits']
            )
            
            # 等待连接稳定
            time.sleep(0.5)
            
            self.is_connected = True
            logger.info(f"USB设备连接成功: {port}")
            return True
            
        except Exception as e:
            logger.error(f"USB设备连接失败: {e}")
            self.is_connected = False
            return False
    
    def disconnect(self):
        """断开USB连接"""
        try:
            if self.serial_port and self.serial_port.is_open:
                self.serial_port.close()
            self.is_connected = False
            logger.info("USB设备已断开连接")
        except Exception as e:
            logger.error(f"断开USB连接时出错: {e}")
    
    def send_command(self, command: bytes) -> bool:
        """发送命令到USB设备"""
        if not self.is_connected or not self.serial_port:
            logger.warning("USB设备未连接，无法发送命令")
            return False
        
        try:
            with self.lock:
                self.serial_port.write(command)
                self.serial_port.flush()
            logger.debug(f"发送命令: {command}")
            return True
        except Exception as e:
            logger.error(f"发送命令失败: {e}")
            return False
    
    def read_response(self, timeout: float = 1.0) -> Optional[str]:
        """读取USB设备响应"""
        if not self.is_connected or not self.serial_port:
            return None
        
        try:
            with self.lock:
                self.serial_port.timeout = timeout
                response = self.serial_port.readline().decode('utf-8').strip()
            logger.debug(f"接收响应: {response}")
            return response
        except Exception as e:
            logger.error(f"读取响应失败: {e}")
            return None
    
    def turn_circuit_on(self) -> bool:
        """接通电路"""
        return self.send_command(COMMANDS['CIRCUIT_ON'])
    
    def turn_circuit_off(self) -> bool:
        """断开电路"""
        return self.send_command(COMMANDS['CIRCUIT_OFF'])
    
    def read_sensor(self) -> Optional[int]:
        """读取传感器状态"""
        if self.send_command(COMMANDS['READ_SENSOR']):
            response = self.read_response()
            if response:
                try:
                    return int(response)
                except ValueError:
                    logger.error(f"传感器响应格式错误: {response}")
        return None
    
    def get_status(self) -> Optional[str]:
        """获取设备状态"""
        if self.send_command(COMMANDS['STATUS']):
            return self.read_response()
        return None
    
    def __del__(self):
        """析构函数，确保连接被正确关闭"""
        self.disconnect()
